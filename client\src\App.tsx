import Header from './components/Header';
import TodoInput from './components/TodoInput';
import TodoList from './components/TodoList';
import Footer from './components/Footer';

function App() {
  return (
    <div className="min-h-screen bg-gray-100 dark:bg-gray-900 transition-colors duration-300">
      {/* Background image (mobile) */}
      <div className="h-48 bg-[url('/bg-mobile-light.jpg')] bg-cover bg-no-repeat bg-center md:bg-[url('/bg-desktop-light.jpg')]"></div>

      <main className="absolute top-12 w-full px-6 md:px-0 md:max-w-xl mx-auto">
        <Header />
        <TodoInput />
        <TodoList />
        <Footer />
      </main>
    </div>
  );
}

export default App;
