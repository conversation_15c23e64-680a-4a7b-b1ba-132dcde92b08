{"version": 3, "sources": ["../../src/index.ts"], "sourcesContent": ["import { rules } from './rules'\nimport type { ESLint, Linter } from 'eslint'\nimport type { RuleModule } from '@typescript-eslint/utils/ts-eslint'\n\ntype RuleKey = keyof typeof rules\n\nexport interface Plugin extends Omit<ESLint.Plugin, 'rules'> {\n  rules: Record<RuleKey, RuleModule<any, any, any>>\n  configs: {\n    recommended: ESLint.ConfigData\n    'flat/recommended': Array<Linter.Config>\n  }\n}\n\nexport const plugin: Plugin = {\n  meta: {\n    name: '@tanstack/eslint-plugin-query',\n  },\n  configs: {} as Plugin['configs'],\n  rules,\n}\n\n// Assign configs here so we can reference `plugin`\nObject.assign(plugin.configs, {\n  recommended: {\n    plugins: ['@tanstack/query'],\n    rules: {\n      '@tanstack/query/exhaustive-deps': 'error',\n      '@tanstack/query/no-rest-destructuring': 'warn',\n      '@tanstack/query/stable-query-client': 'error',\n      '@tanstack/query/no-unstable-deps': 'error',\n      '@tanstack/query/infinite-query-property-order': 'error',\n      '@tanstack/query/no-void-query-fn': 'error',\n      '@tanstack/query/mutation-property-order': 'error',\n    },\n  },\n  'flat/recommended': [\n    {\n      name: 'tanstack/query/flat/recommended',\n      plugins: {\n        '@tanstack/query': plugin,\n      },\n      rules: {\n        '@tanstack/query/exhaustive-deps': 'error',\n        '@tanstack/query/no-rest-destructuring': 'warn',\n        '@tanstack/query/stable-query-client': 'error',\n        '@tanstack/query/no-unstable-deps': 'error',\n        '@tanstack/query/infinite-query-property-order': 'error',\n        '@tanstack/query/no-void-query-fn': 'error',\n        '@tanstack/query/mutation-property-order': 'error',\n      },\n    },\n  ],\n})\n\nexport default plugin\n"], "mappings": ";;;;;AAcO,IAAM,SAAiB;AAAA,EAC5B,MAAM;AAAA,IACJ,MAAM;AAAA,EACR;AAAA,EACA,SAAS,CAAC;AAAA,EACV;AACF;AAGA,OAAO,OAAO,OAAO,SAAS;AAAA,EAC5B,aAAa;AAAA,IACX,SAAS,CAAC,iBAAiB;AAAA,IAC3B,OAAO;AAAA,MACL,mCAAmC;AAAA,MACnC,yCAAyC;AAAA,MACzC,uCAAuC;AAAA,MACvC,oCAAoC;AAAA,MACpC,iDAAiD;AAAA,MACjD,oCAAoC;AAAA,MACpC,2CAA2C;AAAA,IAC7C;AAAA,EACF;AAAA,EACA,oBAAoB;AAAA,IAClB;AAAA,MACE,MAAM;AAAA,MACN,SAAS;AAAA,QACP,mBAAmB;AAAA,MACrB;AAAA,MACA,OAAO;AAAA,QACL,mCAAmC;AAAA,QACnC,yCAAyC;AAAA,QACzC,uCAAuC;AAAA,QACvC,oCAAoC;AAAA,QACpC,iDAAiD;AAAA,QACjD,oCAAoC;AAAA,QACpC,2CAA2C;AAAA,MAC7C;AAAA,IACF;AAAA,EACF;AACF,CAAC;AAED,IAAO,gBAAQ;", "names": []}