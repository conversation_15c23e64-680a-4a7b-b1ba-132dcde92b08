import Todo from "../models/Todo.js";

export const getTodos = async (req, res) => {
  const todos = await Todo.find().sort({ order: 1 });
  res.json(todos);
};

export const createTodo = async (req, res) => {
  const { text } = req.body;
  const count = await Todo.countDocuments();
  const todo = await Todo.create({ text, order: count });
  res.status(201).json(todo);
};

export const updateTodo = async (req, res) => {
  const updated = await Todo.findByIdAndUpdate(req.params.id, req.body, {
    new: true,
  });
  res.json(updated);
};

export const deleteTodo = async (req, res) => {
  await Todo.findByIdAndDelete(req.params.id);
  res.json({ message: "Deleted" });
};

export const clearCompletedTodos = async (req, res) => {
  await Todo.deleteMany({ completed: true });
  res.json({ message: "Cleared completed todos" });
};
