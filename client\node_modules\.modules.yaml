hoistPattern:
  - '*'
hoistedDependencies:
  '@ampproject/remapping@2.3.0':
    '@ampproject/remapping': private
  '@esbuild/aix-ppc64@0.25.5':
    '@esbuild/aix-ppc64': private
  '@esbuild/android-arm64@0.25.5':
    '@esbuild/android-arm64': private
  '@esbuild/android-arm@0.25.5':
    '@esbuild/android-arm': private
  '@esbuild/android-x64@0.25.5':
    '@esbuild/android-x64': private
  '@esbuild/darwin-arm64@0.25.5':
    '@esbuild/darwin-arm64': private
  '@esbuild/darwin-x64@0.25.5':
    '@esbuild/darwin-x64': private
  '@esbuild/freebsd-arm64@0.25.5':
    '@esbuild/freebsd-arm64': private
  '@esbuild/freebsd-x64@0.25.5':
    '@esbuild/freebsd-x64': private
  '@esbuild/linux-arm64@0.25.5':
    '@esbuild/linux-arm64': private
  '@esbuild/linux-arm@0.25.5':
    '@esbuild/linux-arm': private
  '@esbuild/linux-ia32@0.25.5':
    '@esbuild/linux-ia32': private
  '@esbuild/linux-loong64@0.25.5':
    '@esbuild/linux-loong64': private
  '@esbuild/linux-mips64el@0.25.5':
    '@esbuild/linux-mips64el': private
  '@esbuild/linux-ppc64@0.25.5':
    '@esbuild/linux-ppc64': private
  '@esbuild/linux-riscv64@0.25.5':
    '@esbuild/linux-riscv64': private
  '@esbuild/linux-s390x@0.25.5':
    '@esbuild/linux-s390x': private
  '@esbuild/linux-x64@0.25.5':
    '@esbuild/linux-x64': private
  '@esbuild/netbsd-arm64@0.25.5':
    '@esbuild/netbsd-arm64': private
  '@esbuild/netbsd-x64@0.25.5':
    '@esbuild/netbsd-x64': private
  '@esbuild/openbsd-arm64@0.25.5':
    '@esbuild/openbsd-arm64': private
  '@esbuild/openbsd-x64@0.25.5':
    '@esbuild/openbsd-x64': private
  '@esbuild/sunos-x64@0.25.5':
    '@esbuild/sunos-x64': private
  '@esbuild/win32-arm64@0.25.5':
    '@esbuild/win32-arm64': private
  '@esbuild/win32-ia32@0.25.5':
    '@esbuild/win32-ia32': private
  '@esbuild/win32-x64@0.25.5':
    '@esbuild/win32-x64': private
  '@eslint-community/eslint-utils@4.7.0(eslint@9.29.0(jiti@2.4.2))':
    '@eslint-community/eslint-utils': private
  '@eslint-community/regexpp@4.12.1':
    '@eslint-community/regexpp': private
  '@eslint/config-array@0.20.1':
    '@eslint/config-array': private
  '@eslint/config-helpers@0.2.3':
    '@eslint/config-helpers': private
  '@eslint/core@0.14.0':
    '@eslint/core': private
  '@eslint/eslintrc@3.3.1':
    '@eslint/eslintrc': private
  '@eslint/object-schema@2.1.6':
    '@eslint/object-schema': private
  '@eslint/plugin-kit@0.3.2':
    '@eslint/plugin-kit': private
  '@humanfs/core@0.19.1':
    '@humanfs/core': private
  '@humanfs/node@0.16.6':
    '@humanfs/node': private
  '@humanwhocodes/module-importer@1.0.1':
    '@humanwhocodes/module-importer': private
  '@humanwhocodes/retry@0.4.3':
    '@humanwhocodes/retry': private
  '@isaacs/fs-minipass@4.0.1':
    '@isaacs/fs-minipass': private
  '@jridgewell/gen-mapping@0.3.8':
    '@jridgewell/gen-mapping': private
  '@jridgewell/resolve-uri@3.1.2':
    '@jridgewell/resolve-uri': private
  '@jridgewell/set-array@1.2.1':
    '@jridgewell/set-array': private
  '@jridgewell/sourcemap-codec@1.5.0':
    '@jridgewell/sourcemap-codec': private
  '@jridgewell/trace-mapping@0.3.25':
    '@jridgewell/trace-mapping': private
  '@nodelib/fs.scandir@2.1.5':
    '@nodelib/fs.scandir': private
  '@nodelib/fs.stat@2.0.5':
    '@nodelib/fs.stat': private
  '@nodelib/fs.walk@1.2.8':
    '@nodelib/fs.walk': private
  '@rolldown/pluginutils@1.0.0-beta.11':
    '@rolldown/pluginutils': private
  '@rollup/rollup-android-arm-eabi@4.44.0':
    '@rollup/rollup-android-arm-eabi': private
  '@rollup/rollup-android-arm64@4.44.0':
    '@rollup/rollup-android-arm64': private
  '@rollup/rollup-darwin-arm64@4.44.0':
    '@rollup/rollup-darwin-arm64': private
  '@rollup/rollup-darwin-x64@4.44.0':
    '@rollup/rollup-darwin-x64': private
  '@rollup/rollup-freebsd-arm64@4.44.0':
    '@rollup/rollup-freebsd-arm64': private
  '@rollup/rollup-freebsd-x64@4.44.0':
    '@rollup/rollup-freebsd-x64': private
  '@rollup/rollup-linux-arm-gnueabihf@4.44.0':
    '@rollup/rollup-linux-arm-gnueabihf': private
  '@rollup/rollup-linux-arm-musleabihf@4.44.0':
    '@rollup/rollup-linux-arm-musleabihf': private
  '@rollup/rollup-linux-arm64-gnu@4.44.0':
    '@rollup/rollup-linux-arm64-gnu': private
  '@rollup/rollup-linux-arm64-musl@4.44.0':
    '@rollup/rollup-linux-arm64-musl': private
  '@rollup/rollup-linux-loongarch64-gnu@4.44.0':
    '@rollup/rollup-linux-loongarch64-gnu': private
  '@rollup/rollup-linux-powerpc64le-gnu@4.44.0':
    '@rollup/rollup-linux-powerpc64le-gnu': private
  '@rollup/rollup-linux-riscv64-gnu@4.44.0':
    '@rollup/rollup-linux-riscv64-gnu': private
  '@rollup/rollup-linux-riscv64-musl@4.44.0':
    '@rollup/rollup-linux-riscv64-musl': private
  '@rollup/rollup-linux-s390x-gnu@4.44.0':
    '@rollup/rollup-linux-s390x-gnu': private
  '@rollup/rollup-linux-x64-gnu@4.44.0':
    '@rollup/rollup-linux-x64-gnu': private
  '@rollup/rollup-linux-x64-musl@4.44.0':
    '@rollup/rollup-linux-x64-musl': private
  '@rollup/rollup-win32-arm64-msvc@4.44.0':
    '@rollup/rollup-win32-arm64-msvc': private
  '@rollup/rollup-win32-ia32-msvc@4.44.0':
    '@rollup/rollup-win32-ia32-msvc': private
  '@rollup/rollup-win32-x64-msvc@4.44.0':
    '@rollup/rollup-win32-x64-msvc': private
  '@swc/core-darwin-arm64@1.12.5':
    '@swc/core-darwin-arm64': private
  '@swc/core-darwin-x64@1.12.5':
    '@swc/core-darwin-x64': private
  '@swc/core-linux-arm-gnueabihf@1.12.5':
    '@swc/core-linux-arm-gnueabihf': private
  '@swc/core-linux-arm64-gnu@1.12.5':
    '@swc/core-linux-arm64-gnu': private
  '@swc/core-linux-arm64-musl@1.12.5':
    '@swc/core-linux-arm64-musl': private
  '@swc/core-linux-x64-gnu@1.12.5':
    '@swc/core-linux-x64-gnu': private
  '@swc/core-linux-x64-musl@1.12.5':
    '@swc/core-linux-x64-musl': private
  '@swc/core-win32-arm64-msvc@1.12.5':
    '@swc/core-win32-arm64-msvc': private
  '@swc/core-win32-ia32-msvc@1.12.5':
    '@swc/core-win32-ia32-msvc': private
  '@swc/core-win32-x64-msvc@1.12.5':
    '@swc/core-win32-x64-msvc': private
  '@swc/core@1.12.5':
    '@swc/core': private
  '@swc/counter@0.1.3':
    '@swc/counter': private
  '@swc/types@0.1.23':
    '@swc/types': private
  '@tailwindcss/node@4.1.10':
    '@tailwindcss/node': private
  '@tailwindcss/oxide-android-arm64@4.1.10':
    '@tailwindcss/oxide-android-arm64': private
  '@tailwindcss/oxide-darwin-arm64@4.1.10':
    '@tailwindcss/oxide-darwin-arm64': private
  '@tailwindcss/oxide-darwin-x64@4.1.10':
    '@tailwindcss/oxide-darwin-x64': private
  '@tailwindcss/oxide-freebsd-x64@4.1.10':
    '@tailwindcss/oxide-freebsd-x64': private
  '@tailwindcss/oxide-linux-arm-gnueabihf@4.1.10':
    '@tailwindcss/oxide-linux-arm-gnueabihf': private
  '@tailwindcss/oxide-linux-arm64-gnu@4.1.10':
    '@tailwindcss/oxide-linux-arm64-gnu': private
  '@tailwindcss/oxide-linux-arm64-musl@4.1.10':
    '@tailwindcss/oxide-linux-arm64-musl': private
  '@tailwindcss/oxide-linux-x64-gnu@4.1.10':
    '@tailwindcss/oxide-linux-x64-gnu': private
  '@tailwindcss/oxide-linux-x64-musl@4.1.10':
    '@tailwindcss/oxide-linux-x64-musl': private
  '@tailwindcss/oxide-wasm32-wasi@4.1.10':
    '@tailwindcss/oxide-wasm32-wasi': private
  '@tailwindcss/oxide-win32-arm64-msvc@4.1.10':
    '@tailwindcss/oxide-win32-arm64-msvc': private
  '@tailwindcss/oxide-win32-x64-msvc@4.1.10':
    '@tailwindcss/oxide-win32-x64-msvc': private
  '@tailwindcss/oxide@4.1.10':
    '@tailwindcss/oxide': private
  '@tanstack/query-core@5.81.2':
    '@tanstack/query-core': private
  '@types/estree@1.0.8':
    '@types/estree': private
  '@types/json-schema@7.0.15':
    '@types/json-schema': private
  '@typescript-eslint/eslint-plugin@8.34.1(@typescript-eslint/parser@8.34.1(eslint@9.29.0(jiti@2.4.2))(typescript@5.8.3))(eslint@9.29.0(jiti@2.4.2))(typescript@5.8.3)':
    '@typescript-eslint/eslint-plugin': private
  '@typescript-eslint/parser@8.34.1(eslint@9.29.0(jiti@2.4.2))(typescript@5.8.3)':
    '@typescript-eslint/parser': private
  '@typescript-eslint/project-service@8.34.1(typescript@5.8.3)':
    '@typescript-eslint/project-service': private
  '@typescript-eslint/scope-manager@8.34.1':
    '@typescript-eslint/scope-manager': private
  '@typescript-eslint/tsconfig-utils@8.34.1(typescript@5.8.3)':
    '@typescript-eslint/tsconfig-utils': private
  '@typescript-eslint/type-utils@8.34.1(eslint@9.29.0(jiti@2.4.2))(typescript@5.8.3)':
    '@typescript-eslint/type-utils': private
  '@typescript-eslint/types@8.34.1':
    '@typescript-eslint/types': private
  '@typescript-eslint/typescript-estree@8.34.1(typescript@5.8.3)':
    '@typescript-eslint/typescript-estree': private
  '@typescript-eslint/utils@8.34.1(eslint@9.29.0(jiti@2.4.2))(typescript@5.8.3)':
    '@typescript-eslint/utils': private
  '@typescript-eslint/visitor-keys@8.34.1':
    '@typescript-eslint/visitor-keys': private
  acorn-jsx@5.3.2(acorn@8.15.0):
    acorn-jsx: private
  acorn@8.15.0:
    acorn: private
  ajv@6.12.6:
    ajv: private
  ansi-styles@4.3.0:
    ansi-styles: private
  argparse@2.0.1:
    argparse: private
  balanced-match@1.0.2:
    balanced-match: private
  brace-expansion@1.1.12:
    brace-expansion: private
  braces@3.0.3:
    braces: private
  callsites@3.1.0:
    callsites: private
  chalk@4.1.2:
    chalk: private
  chownr@3.0.0:
    chownr: private
  color-convert@2.0.1:
    color-convert: private
  color-name@1.1.4:
    color-name: private
  concat-map@0.0.1:
    concat-map: private
  cross-spawn@7.0.6:
    cross-spawn: private
  csstype@3.1.3:
    csstype: private
  debug@4.4.1:
    debug: private
  deep-is@0.1.4:
    deep-is: private
  detect-libc@2.0.4:
    detect-libc: private
  enhanced-resolve@5.18.1:
    enhanced-resolve: private
  esbuild@0.25.5:
    esbuild: private
  escape-string-regexp@4.0.0:
    escape-string-regexp: private
  eslint-scope@8.4.0:
    eslint-scope: private
  eslint-visitor-keys@4.2.1:
    eslint-visitor-keys: private
  espree@10.4.0:
    espree: private
  esquery@1.6.0:
    esquery: private
  esrecurse@4.3.0:
    esrecurse: private
  estraverse@5.3.0:
    estraverse: private
  esutils@2.0.3:
    esutils: private
  fast-deep-equal@3.1.3:
    fast-deep-equal: private
  fast-glob@3.3.3:
    fast-glob: private
  fast-json-stable-stringify@2.1.0:
    fast-json-stable-stringify: private
  fast-levenshtein@2.0.6:
    fast-levenshtein: private
  fastq@1.19.1:
    fastq: private
  fdir@6.4.6(picomatch@4.0.2):
    fdir: private
  file-entry-cache@8.0.0:
    file-entry-cache: private
  fill-range@7.1.1:
    fill-range: private
  find-up@5.0.0:
    find-up: private
  flat-cache@4.0.1:
    flat-cache: private
  flatted@3.3.3:
    flatted: private
  fsevents@2.3.3:
    fsevents: private
  glob-parent@6.0.2:
    glob-parent: private
  graceful-fs@4.2.11:
    graceful-fs: private
  graphemer@1.4.0:
    graphemer: private
  has-flag@4.0.0:
    has-flag: private
  ignore@5.3.2:
    ignore: private
  import-fresh@3.3.1:
    import-fresh: private
  imurmurhash@0.1.4:
    imurmurhash: private
  is-extglob@2.1.1:
    is-extglob: private
  is-glob@4.0.3:
    is-glob: private
  is-number@7.0.0:
    is-number: private
  isexe@2.0.0:
    isexe: private
  jiti@2.4.2:
    jiti: private
  js-yaml@4.1.0:
    js-yaml: private
  json-buffer@3.0.1:
    json-buffer: private
  json-schema-traverse@0.4.1:
    json-schema-traverse: private
  json-stable-stringify-without-jsonify@1.0.1:
    json-stable-stringify-without-jsonify: private
  keyv@4.5.4:
    keyv: private
  levn@0.4.1:
    levn: private
  lightningcss-darwin-arm64@1.30.1:
    lightningcss-darwin-arm64: private
  lightningcss-darwin-x64@1.30.1:
    lightningcss-darwin-x64: private
  lightningcss-freebsd-x64@1.30.1:
    lightningcss-freebsd-x64: private
  lightningcss-linux-arm-gnueabihf@1.30.1:
    lightningcss-linux-arm-gnueabihf: private
  lightningcss-linux-arm64-gnu@1.30.1:
    lightningcss-linux-arm64-gnu: private
  lightningcss-linux-arm64-musl@1.30.1:
    lightningcss-linux-arm64-musl: private
  lightningcss-linux-x64-gnu@1.30.1:
    lightningcss-linux-x64-gnu: private
  lightningcss-linux-x64-musl@1.30.1:
    lightningcss-linux-x64-musl: private
  lightningcss-win32-arm64-msvc@1.30.1:
    lightningcss-win32-arm64-msvc: private
  lightningcss-win32-x64-msvc@1.30.1:
    lightningcss-win32-x64-msvc: private
  lightningcss@1.30.1:
    lightningcss: private
  locate-path@6.0.0:
    locate-path: private
  lodash.merge@4.6.2:
    lodash.merge: private
  magic-string@0.30.17:
    magic-string: private
  merge2@1.4.1:
    merge2: private
  micromatch@4.0.8:
    micromatch: private
  minimatch@3.1.2:
    minimatch: private
  minipass@7.1.2:
    minipass: private
  minizlib@3.0.2:
    minizlib: private
  mkdirp@3.0.1:
    mkdirp: private
  ms@2.1.3:
    ms: private
  nanoid@3.3.11:
    nanoid: private
  natural-compare@1.4.0:
    natural-compare: private
  optionator@0.9.4:
    optionator: private
  p-limit@3.1.0:
    p-limit: private
  p-locate@5.0.0:
    p-locate: private
  parent-module@1.0.1:
    parent-module: private
  path-exists@4.0.0:
    path-exists: private
  path-key@3.1.1:
    path-key: private
  picocolors@1.1.1:
    picocolors: private
  picomatch@4.0.2:
    picomatch: private
  postcss@8.5.6:
    postcss: private
  prelude-ls@1.2.1:
    prelude-ls: private
  punycode@2.3.1:
    punycode: private
  queue-microtask@1.2.3:
    queue-microtask: private
  resolve-from@4.0.0:
    resolve-from: private
  reusify@1.1.0:
    reusify: private
  rollup@4.44.0:
    rollup: private
  run-parallel@1.2.0:
    run-parallel: private
  scheduler@0.26.0:
    scheduler: private
  semver@7.7.2:
    semver: private
  shebang-command@2.0.0:
    shebang-command: private
  shebang-regex@3.0.0:
    shebang-regex: private
  source-map-js@1.2.1:
    source-map-js: private
  strip-json-comments@3.1.1:
    strip-json-comments: private
  supports-color@7.2.0:
    supports-color: private
  tapable@2.2.2:
    tapable: private
  tar@7.4.3:
    tar: private
  tinyglobby@0.2.14:
    tinyglobby: private
  to-regex-range@5.0.1:
    to-regex-range: private
  ts-api-utils@2.1.0(typescript@5.8.3):
    ts-api-utils: private
  type-check@0.4.0:
    type-check: private
  uri-js@4.4.1:
    uri-js: private
  which@2.0.2:
    which: private
  word-wrap@1.2.5:
    word-wrap: private
  yallist@5.0.0:
    yallist: private
  yocto-queue@0.1.0:
    yocto-queue: private
ignoredBuilds:
  - '@tailwindcss/oxide'
included:
  dependencies: true
  devDependencies: true
  optionalDependencies: true
injectedDeps: {}
layoutVersion: 5
nodeLinker: isolated
packageManager: pnpm@10.12.2
pendingBuilds: []
prunedAt: Mon, 23 Jun 2025 09:56:09 GMT
publicHoistPattern: []
registries:
  '@jsr': https://npm.jsr.io/
  default: https://registry.npmjs.org/
skipped:
  - '@esbuild/aix-ppc64@0.25.5'
  - '@esbuild/android-arm64@0.25.5'
  - '@esbuild/android-arm@0.25.5'
  - '@esbuild/android-x64@0.25.5'
  - '@esbuild/darwin-arm64@0.25.5'
  - '@esbuild/darwin-x64@0.25.5'
  - '@esbuild/freebsd-arm64@0.25.5'
  - '@esbuild/freebsd-x64@0.25.5'
  - '@esbuild/linux-arm64@0.25.5'
  - '@esbuild/linux-arm@0.25.5'
  - '@esbuild/linux-ia32@0.25.5'
  - '@esbuild/linux-loong64@0.25.5'
  - '@esbuild/linux-mips64el@0.25.5'
  - '@esbuild/linux-ppc64@0.25.5'
  - '@esbuild/linux-riscv64@0.25.5'
  - '@esbuild/linux-s390x@0.25.5'
  - '@esbuild/linux-x64@0.25.5'
  - '@esbuild/netbsd-arm64@0.25.5'
  - '@esbuild/netbsd-x64@0.25.5'
  - '@esbuild/openbsd-arm64@0.25.5'
  - '@esbuild/openbsd-x64@0.25.5'
  - '@esbuild/sunos-x64@0.25.5'
  - '@esbuild/win32-arm64@0.25.5'
  - '@esbuild/win32-ia32@0.25.5'
  - '@rollup/rollup-android-arm-eabi@4.44.0'
  - '@rollup/rollup-android-arm64@4.44.0'
  - '@rollup/rollup-darwin-arm64@4.44.0'
  - '@rollup/rollup-darwin-x64@4.44.0'
  - '@rollup/rollup-freebsd-arm64@4.44.0'
  - '@rollup/rollup-freebsd-x64@4.44.0'
  - '@rollup/rollup-linux-arm-gnueabihf@4.44.0'
  - '@rollup/rollup-linux-arm-musleabihf@4.44.0'
  - '@rollup/rollup-linux-arm64-gnu@4.44.0'
  - '@rollup/rollup-linux-arm64-musl@4.44.0'
  - '@rollup/rollup-linux-loongarch64-gnu@4.44.0'
  - '@rollup/rollup-linux-powerpc64le-gnu@4.44.0'
  - '@rollup/rollup-linux-riscv64-gnu@4.44.0'
  - '@rollup/rollup-linux-riscv64-musl@4.44.0'
  - '@rollup/rollup-linux-s390x-gnu@4.44.0'
  - '@rollup/rollup-linux-x64-gnu@4.44.0'
  - '@rollup/rollup-linux-x64-musl@4.44.0'
  - '@rollup/rollup-win32-arm64-msvc@4.44.0'
  - '@rollup/rollup-win32-ia32-msvc@4.44.0'
  - '@swc/core-darwin-arm64@1.12.5'
  - '@swc/core-darwin-x64@1.12.5'
  - '@swc/core-linux-arm-gnueabihf@1.12.5'
  - '@swc/core-linux-arm64-gnu@1.12.5'
  - '@swc/core-linux-arm64-musl@1.12.5'
  - '@swc/core-linux-x64-gnu@1.12.5'
  - '@swc/core-linux-x64-musl@1.12.5'
  - '@swc/core-win32-arm64-msvc@1.12.5'
  - '@swc/core-win32-ia32-msvc@1.12.5'
  - '@tailwindcss/oxide-android-arm64@4.1.10'
  - '@tailwindcss/oxide-darwin-arm64@4.1.10'
  - '@tailwindcss/oxide-darwin-x64@4.1.10'
  - '@tailwindcss/oxide-freebsd-x64@4.1.10'
  - '@tailwindcss/oxide-linux-arm-gnueabihf@4.1.10'
  - '@tailwindcss/oxide-linux-arm64-gnu@4.1.10'
  - '@tailwindcss/oxide-linux-arm64-musl@4.1.10'
  - '@tailwindcss/oxide-linux-x64-gnu@4.1.10'
  - '@tailwindcss/oxide-linux-x64-musl@4.1.10'
  - '@tailwindcss/oxide-wasm32-wasi@4.1.10'
  - '@tailwindcss/oxide-win32-arm64-msvc@4.1.10'
  - fsevents@2.3.3
  - lightningcss-darwin-arm64@1.30.1
  - lightningcss-darwin-x64@1.30.1
  - lightningcss-freebsd-x64@1.30.1
  - lightningcss-linux-arm-gnueabihf@1.30.1
  - lightningcss-linux-arm64-gnu@1.30.1
  - lightningcss-linux-arm64-musl@1.30.1
  - lightningcss-linux-x64-gnu@1.30.1
  - lightningcss-linux-x64-musl@1.30.1
  - lightningcss-win32-arm64-msvc@1.30.1
storeDir: C:\Users\<USER>\AppData\Local\pnpm\store\v10
virtualStoreDir: C:\Users\<USER>\Desktop\web Dev\todo\client\node_modules\.pnpm
virtualStoreDirMaxLength: 60
