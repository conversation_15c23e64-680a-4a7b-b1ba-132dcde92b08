{"name": "@tanstack/eslint-plugin-query", "version": "5.81.2", "description": "ESLint plugin for TanStack Query", "author": "<PERSON><PERSON>", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/TanStack/query.git", "directory": "packages/eslint-plugin-query"}, "homepage": "https://tanstack.com/query", "funding": {"type": "github", "url": "https://github.com/sponsors/tanner<PERSON>ley"}, "type": "module", "types": "build/legacy/index.d.ts", "main": "build/legacy/index.cjs", "module": "build/legacy/index.js", "react-native": "src/index.ts", "exports": {".": {"@tanstack/custom-condition": "./src/index.ts", "import": {"types": "./build/modern/index.d.ts", "default": "./build/modern/index.js"}, "require": {"types": "./build/modern/index.d.cts", "default": "./build/modern/index.cjs"}}, "./package.json": "./package.json"}, "sideEffects": false, "files": ["build", "src", "!src/__tests__"], "dependencies": {"@typescript-eslint/utils": "^8.18.1"}, "devDependencies": {"@typescript-eslint/parser": "^8.18.1", "@typescript-eslint/rule-tester": "^8.18.1", "combinate": "^1.1.11", "eslint": "^9.15.0", "npm-run-all2": "^5.0.0"}, "peerDependencies": {"eslint": "^8.57.0 || ^9.0.0"}, "scripts": {}}