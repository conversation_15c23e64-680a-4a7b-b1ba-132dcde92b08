{"version": 3, "sources": ["../../src/mutationCache.ts"], "sourcesContent": ["import { notify<PERSON><PERSON><PERSON> } from './notifyManager'\nimport { Mutation } from './mutation'\nimport { matchMutation, noop } from './utils'\nimport { Subscribable } from './subscribable'\nimport type { MutationObserver } from './mutationObserver'\nimport type { DefaultError, MutationOptions, NotifyEvent } from './types'\nimport type { QueryClient } from './queryClient'\nimport type { Action, MutationState } from './mutation'\nimport type { MutationFilters } from './utils'\n\n// TYPES\n\ninterface MutationCacheConfig {\n  onError?: (\n    error: DefaultError,\n    variables: unknown,\n    context: unknown,\n    mutation: Mutation<unknown, unknown, unknown>,\n  ) => Promise<unknown> | unknown\n  onSuccess?: (\n    data: unknown,\n    variables: unknown,\n    context: unknown,\n    mutation: Mutation<unknown, unknown, unknown>,\n  ) => Promise<unknown> | unknown\n  onMutate?: (\n    variables: unknown,\n    mutation: Mutation<unknown, unknown, unknown>,\n  ) => Promise<unknown> | unknown\n  onSettled?: (\n    data: unknown | undefined,\n    error: DefaultError | null,\n    variables: unknown,\n    context: unknown,\n    mutation: Mutation<unknown, unknown, unknown>,\n  ) => Promise<unknown> | unknown\n}\n\ninterface NotifyEventMutationAdded extends NotifyEvent {\n  type: 'added'\n  mutation: Mutation<any, any, any, any>\n}\ninterface NotifyEventMutationRemoved extends NotifyEvent {\n  type: 'removed'\n  mutation: Mutation<any, any, any, any>\n}\n\ninterface NotifyEventMutationObserverAdded extends NotifyEvent {\n  type: 'observerAdded'\n  mutation: Mutation<any, any, any, any>\n  observer: MutationObserver<any, any, any>\n}\n\ninterface NotifyEventMutationObserverRemoved extends NotifyEvent {\n  type: 'observerRemoved'\n  mutation: Mutation<any, any, any, any>\n  observer: MutationObserver<any, any, any>\n}\n\ninterface NotifyEventMutationObserverOptionsUpdated extends NotifyEvent {\n  type: 'observerOptionsUpdated'\n  mutation?: Mutation<any, any, any, any>\n  observer: MutationObserver<any, any, any, any>\n}\n\ninterface NotifyEventMutationUpdated extends NotifyEvent {\n  type: 'updated'\n  mutation: Mutation<any, any, any, any>\n  action: Action<any, any, any, any>\n}\n\nexport type MutationCacheNotifyEvent =\n  | NotifyEventMutationAdded\n  | NotifyEventMutationRemoved\n  | NotifyEventMutationObserverAdded\n  | NotifyEventMutationObserverRemoved\n  | NotifyEventMutationObserverOptionsUpdated\n  | NotifyEventMutationUpdated\n\ntype MutationCacheListener = (event: MutationCacheNotifyEvent) => void\n\n// CLASS\n\nexport class MutationCache extends Subscribable<MutationCacheListener> {\n  #mutations: Set<Mutation<any, any, any, any>>\n  #scopes: Map<string, Array<Mutation<any, any, any, any>>>\n  #mutationId: number\n\n  constructor(public config: MutationCacheConfig = {}) {\n    super()\n    this.#mutations = new Set()\n    this.#scopes = new Map()\n    this.#mutationId = 0\n  }\n\n  build<TData, TError, TVariables, TContext>(\n    client: QueryClient,\n    options: MutationOptions<TData, TError, TVariables, TContext>,\n    state?: MutationState<TData, TError, TVariables, TContext>,\n  ): Mutation<TData, TError, TVariables, TContext> {\n    const mutation = new Mutation({\n      mutationCache: this,\n      mutationId: ++this.#mutationId,\n      options: client.defaultMutationOptions(options),\n      state,\n    })\n\n    this.add(mutation)\n\n    return mutation\n  }\n\n  add(mutation: Mutation<any, any, any, any>): void {\n    this.#mutations.add(mutation)\n    const scope = scopeFor(mutation)\n    if (typeof scope === 'string') {\n      const scopedMutations = this.#scopes.get(scope)\n      if (scopedMutations) {\n        scopedMutations.push(mutation)\n      } else {\n        this.#scopes.set(scope, [mutation])\n      }\n    }\n    this.notify({ type: 'added', mutation })\n  }\n\n  remove(mutation: Mutation<any, any, any, any>): void {\n    if (this.#mutations.delete(mutation)) {\n      const scope = scopeFor(mutation)\n      if (typeof scope === 'string') {\n        const scopedMutations = this.#scopes.get(scope)\n        if (scopedMutations) {\n          if (scopedMutations.length > 1) {\n            const index = scopedMutations.indexOf(mutation)\n            if (index !== -1) {\n              scopedMutations.splice(index, 1)\n            }\n          } else if (scopedMutations[0] === mutation) {\n            this.#scopes.delete(scope)\n          }\n        }\n      }\n    }\n\n    // Currently we notify the removal even if the mutation was already removed.\n    // Consider making this an error or not notifying of the removal depending on the desired semantics.\n    this.notify({ type: 'removed', mutation })\n  }\n\n  canRun(mutation: Mutation<any, any, any, any>): boolean {\n    const scope = scopeFor(mutation)\n    if (typeof scope === 'string') {\n      const mutationsWithSameScope = this.#scopes.get(scope)\n      const firstPendingMutation = mutationsWithSameScope?.find(\n        (m) => m.state.status === 'pending',\n      )\n      // we can run if there is no current pending mutation (start use-case)\n      // or if WE are the first pending mutation (continue use-case)\n      return !firstPendingMutation || firstPendingMutation === mutation\n    } else {\n      // For unscoped mutations there are never any pending mutations in front of the\n      // current mutation\n      return true\n    }\n  }\n\n  runNext(mutation: Mutation<any, any, any, any>): Promise<unknown> {\n    const scope = scopeFor(mutation)\n    if (typeof scope === 'string') {\n      const foundMutation = this.#scopes\n        .get(scope)\n        ?.find((m) => m !== mutation && m.state.isPaused)\n\n      return foundMutation?.continue() ?? Promise.resolve()\n    } else {\n      return Promise.resolve()\n    }\n  }\n\n  clear(): void {\n    notifyManager.batch(() => {\n      this.#mutations.forEach((mutation) => {\n        this.notify({ type: 'removed', mutation })\n      })\n      this.#mutations.clear()\n      this.#scopes.clear()\n    })\n  }\n\n  getAll(): Array<Mutation> {\n    return Array.from(this.#mutations)\n  }\n\n  find<\n    TData = unknown,\n    TError = DefaultError,\n    TVariables = any,\n    TContext = unknown,\n  >(\n    filters: MutationFilters,\n  ): Mutation<TData, TError, TVariables, TContext> | undefined {\n    const defaultedFilters = { exact: true, ...filters }\n\n    return this.getAll().find((mutation) =>\n      matchMutation(defaultedFilters, mutation),\n    ) as Mutation<TData, TError, TVariables, TContext> | undefined\n  }\n\n  findAll(filters: MutationFilters = {}): Array<Mutation> {\n    return this.getAll().filter((mutation) => matchMutation(filters, mutation))\n  }\n\n  notify(event: MutationCacheNotifyEvent) {\n    notifyManager.batch(() => {\n      this.listeners.forEach((listener) => {\n        listener(event)\n      })\n    })\n  }\n\n  resumePausedMutations(): Promise<unknown> {\n    const pausedMutations = this.getAll().filter((x) => x.state.isPaused)\n\n    return notifyManager.batch(() =>\n      Promise.all(\n        pausedMutations.map((mutation) => mutation.continue().catch(noop)),\n      ),\n    )\n  }\n}\n\nfunction scopeFor(mutation: Mutation<any, any, any, any>) {\n  return mutation.options.scope?.id\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,2BAA8B;AAC9B,sBAAyB;AACzB,mBAAoC;AACpC,0BAA6B;AAgFtB,IAAM,gBAAN,cAA4B,iCAAoC;AAAA,EAKrE,YAAmB,SAA8B,CAAC,GAAG;AACnD,UAAM;AADW;AAEjB,SAAK,aAAa,oBAAI,IAAI;AAC1B,SAAK,UAAU,oBAAI,IAAI;AACvB,SAAK,cAAc;AAAA,EACrB;AAAA,EATA;AAAA,EACA;AAAA,EACA;AAAA,EASA,MACE,QACA,SACA,OAC+C;AAC/C,UAAM,WAAW,IAAI,yBAAS;AAAA,MAC5B,eAAe;AAAA,MACf,YAAY,EAAE,KAAK;AAAA,MACnB,SAAS,OAAO,uBAAuB,OAAO;AAAA,MAC9C;AAAA,IACF,CAAC;AAED,SAAK,IAAI,QAAQ;AAEjB,WAAO;AAAA,EACT;AAAA,EAEA,IAAI,UAA8C;AAChD,SAAK,WAAW,IAAI,QAAQ;AAC5B,UAAM,QAAQ,SAAS,QAAQ;AAC/B,QAAI,OAAO,UAAU,UAAU;AAC7B,YAAM,kBAAkB,KAAK,QAAQ,IAAI,KAAK;AAC9C,UAAI,iBAAiB;AACnB,wBAAgB,KAAK,QAAQ;AAAA,MAC/B,OAAO;AACL,aAAK,QAAQ,IAAI,OAAO,CAAC,QAAQ,CAAC;AAAA,MACpC;AAAA,IACF;AACA,SAAK,OAAO,EAAE,MAAM,SAAS,SAAS,CAAC;AAAA,EACzC;AAAA,EAEA,OAAO,UAA8C;AACnD,QAAI,KAAK,WAAW,OAAO,QAAQ,GAAG;AACpC,YAAM,QAAQ,SAAS,QAAQ;AAC/B,UAAI,OAAO,UAAU,UAAU;AAC7B,cAAM,kBAAkB,KAAK,QAAQ,IAAI,KAAK;AAC9C,YAAI,iBAAiB;AACnB,cAAI,gBAAgB,SAAS,GAAG;AAC9B,kBAAM,QAAQ,gBAAgB,QAAQ,QAAQ;AAC9C,gBAAI,UAAU,IAAI;AAChB,8BAAgB,OAAO,OAAO,CAAC;AAAA,YACjC;AAAA,UACF,WAAW,gBAAgB,CAAC,MAAM,UAAU;AAC1C,iBAAK,QAAQ,OAAO,KAAK;AAAA,UAC3B;AAAA,QACF;AAAA,MACF;AAAA,IACF;AAIA,SAAK,OAAO,EAAE,MAAM,WAAW,SAAS,CAAC;AAAA,EAC3C;AAAA,EAEA,OAAO,UAAiD;AACtD,UAAM,QAAQ,SAAS,QAAQ;AAC/B,QAAI,OAAO,UAAU,UAAU;AAC7B,YAAM,yBAAyB,KAAK,QAAQ,IAAI,KAAK;AACrD,YAAM,uBAAuB,wBAAwB;AAAA,QACnD,CAAC,MAAM,EAAE,MAAM,WAAW;AAAA,MAC5B;AAGA,aAAO,CAAC,wBAAwB,yBAAyB;AAAA,IAC3D,OAAO;AAGL,aAAO;AAAA,IACT;AAAA,EACF;AAAA,EAEA,QAAQ,UAA0D;AAChE,UAAM,QAAQ,SAAS,QAAQ;AAC/B,QAAI,OAAO,UAAU,UAAU;AAC7B,YAAM,gBAAgB,KAAK,QACxB,IAAI,KAAK,GACR,KAAK,CAAC,MAAM,MAAM,YAAY,EAAE,MAAM,QAAQ;AAElD,aAAO,eAAe,SAAS,KAAK,QAAQ,QAAQ;AAAA,IACtD,OAAO;AACL,aAAO,QAAQ,QAAQ;AAAA,IACzB;AAAA,EACF;AAAA,EAEA,QAAc;AACZ,uCAAc,MAAM,MAAM;AACxB,WAAK,WAAW,QAAQ,CAAC,aAAa;AACpC,aAAK,OAAO,EAAE,MAAM,WAAW,SAAS,CAAC;AAAA,MAC3C,CAAC;AACD,WAAK,WAAW,MAAM;AACtB,WAAK,QAAQ,MAAM;AAAA,IACrB,CAAC;AAAA,EACH;AAAA,EAEA,SAA0B;AACxB,WAAO,MAAM,KAAK,KAAK,UAAU;AAAA,EACnC;AAAA,EAEA,KAME,SAC2D;AAC3D,UAAM,mBAAmB,EAAE,OAAO,MAAM,GAAG,QAAQ;AAEnD,WAAO,KAAK,OAAO,EAAE;AAAA,MAAK,CAAC,iBACzB,4BAAc,kBAAkB,QAAQ;AAAA,IAC1C;AAAA,EACF;AAAA,EAEA,QAAQ,UAA2B,CAAC,GAAoB;AACtD,WAAO,KAAK,OAAO,EAAE,OAAO,CAAC,iBAAa,4BAAc,SAAS,QAAQ,CAAC;AAAA,EAC5E;AAAA,EAEA,OAAO,OAAiC;AACtC,uCAAc,MAAM,MAAM;AACxB,WAAK,UAAU,QAAQ,CAAC,aAAa;AACnC,iBAAS,KAAK;AAAA,MAChB,CAAC;AAAA,IACH,CAAC;AAAA,EACH;AAAA,EAEA,wBAA0C;AACxC,UAAM,kBAAkB,KAAK,OAAO,EAAE,OAAO,CAAC,MAAM,EAAE,MAAM,QAAQ;AAEpE,WAAO,mCAAc;AAAA,MAAM,MACzB,QAAQ;AAAA,QACN,gBAAgB,IAAI,CAAC,aAAa,SAAS,SAAS,EAAE,MAAM,iBAAI,CAAC;AAAA,MACnE;AAAA,IACF;AAAA,EACF;AACF;AAEA,SAAS,SAAS,UAAwC;AACxD,SAAO,SAAS,QAAQ,OAAO;AACjC;", "names": []}