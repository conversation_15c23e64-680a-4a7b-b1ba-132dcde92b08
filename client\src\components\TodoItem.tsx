import { Todo } from '../types/todo';

function TodoItem({ todo }: { todo: Todo }) {
  return (
    <li className="flex items-center px-4 py-3 border-b border-gray-200 dark:border-gray-700">
      <input
        type="checkbox"
        checked={todo.completed}
        readOnly
        className="mr-4 w-5 h-5 accent-blue-500"
      />
      <span className={`flex-1 ${todo.completed ? 'line-through text-gray-400' : ''}`}>
        {todo.text}
      </span>
    </li>
  );
}

export default TodoItem;
