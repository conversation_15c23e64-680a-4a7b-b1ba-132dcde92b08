import { useQuery } from '@tanstack/react-query';
import { fetchTodos } from '../api/todoApi';
import TodoItem from './TodoItem';

function TodoList() {
  const { data: todos, isLoading } = useQuery({ queryKey: ['todos'], queryFn: fetchTodos });

  if (isLoading) return <p>Loading...</p>;

  return (
    <div className="rounded-md overflow-hidden shadow">
      <ul className="bg-white dark:bg-gray-800">
        {todos?.map((todo) => (
          <TodoItem key={todo._id} todo={todo} />
        ))}
      </ul>
    </div>
  );
}

export default TodoList;
